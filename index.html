<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kalkulator Sederhana</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="container">
      <div class="calculator">
        <div class="display">
          <input type="text" id="result" readonly />
        </div>
        <div class="buttons">
          <button class="btn clear" onclick="clearDisplay()">C</button>
          <button class="btn operator" onclick="deleteLast()">⌫</button>
          <button class="btn operator" onclick="appendToDisplay('/')">/</button>
          <button class="btn operator" onclick="appendToDisplay('*')">×</button>

          <button class="btn number" onclick="appendToDisplay('7')">7</button>
          <button class="btn number" onclick="appendToDisplay('8')">8</button>
          <button class="btn number" onclick="appendToDisplay('9')">9</button>
          <button class="btn operator" onclick="appendToDisplay('-')">-</button>

          <button class="btn number" onclick="appendToDisplay('4')">4</button>
          <button class="btn number" onclick="appendToDisplay('5')">5</button>
          <button class="btn number" onclick="appendToDisplay('6')">6</button>
          <button class="btn operator" onclick="appendToDisplay('+')">+</button>

          <button class="btn number" onclick="appendToDisplay('1')">1</button>
          <button class="btn number" onclick="appendToDisplay('2')">2</button>
          <button class="btn number" onclick="appendToDisplay('3')">3</button>
          <button class="btn equals" onclick="calculate()">=</button>

          <button class="btn number zero" onclick="appendToDisplay('0')">
            0
          </button>
          <button class="btn number" onclick="appendToDisplay('.')">.</button>
          <button class="btn equals" onclick="calculate()">=</button>
        </div>
      </div>
    </div>
    <script src="script.js"></script>
  </body>
</html>
