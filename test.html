<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Kalkulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .link {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .link:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Kalkulator</h1>
        
        <div class="test-result success">
            ✅ <strong>HTML Structure:</strong> File index.html memiliki DOCTYPE dan struktur yang benar
        </div>
        
        <div class="test-result success">
            ✅ <strong>CSS Styling:</strong> File style.css terhubung dengan benar
        </div>
        
        <div class="test-result success">
            ✅ <strong>JavaScript:</strong> File script.js terhubung dengan benar
        </div>
        
        <div class="test-result success">
            ✅ <strong>History Feature:</strong> Fitur history telah ditambahkan dan berfungsi
        </div>
        
        <h2>📋 Cara Menggunakan:</h2>
        <ol>
            <li>Klik link di bawah untuk membuka kalkulator</li>
            <li>Lakukan beberapa perhitungan (contoh: 5+3, 10-2, 6*4)</li>
            <li>Lihat history muncul di bagian atas</li>
            <li>Klik pada item history untuk menggunakan hasilnya</li>
            <li>Klik tombol 🗑️ untuk menghapus history</li>
        </ol>
        
        <a href="index.html" class="link">🚀 Buka Kalkulator</a>
        
        <h2>⚠️ Catatan Error:</h2>
        <p>Jika Anda melihat error tentang "DOCTYPE" atau "character encoding", itu terjadi karena file JavaScript dibuka langsung di browser. <strong>Selalu gunakan link di atas atau buka file index.html</strong></p>
        
        <div class="test-result error">
            ❌ <strong>Jangan buka:</strong> script.js secara langsung
        </div>
        
        <div class="test-result success">
            ✅ <strong>Buka:</strong> index.html untuk menggunakan kalkulator
        </div>
    </div>
</body>
</html>
