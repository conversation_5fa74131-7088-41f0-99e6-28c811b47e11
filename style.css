* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Arial", sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.container {
  padding: 20px;
}

.calculator {
  background: #2c3e50;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 350px;
  width: 100%;
}

.history-section {
  margin-bottom: 20px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  color: #bdc3c7;
  font-size: 0.9rem;
}

.clear-history {
  background: #e74c3c;
  border: none;
  border-radius: 5px;
  color: white;
  padding: 5px 8px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.2s ease;
}

.clear-history:hover {
  background: #c0392b;
}

.history-display {
  background: #34495e;
  border-radius: 10px;
  padding: 15px;
  max-height: 150px;
  overflow-y: auto;
  min-height: 60px;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

.history-item {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin-bottom: 8px;
  text-align: right;
  line-height: 1.4;
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-expression {
  color: #ecf0f1;
  font-size: 1rem;
}

.history-result {
  color: #3498db;
  font-weight: bold;
  font-size: 1.1rem;
}

.history-display::-webkit-scrollbar {
  width: 6px;
}

.history-display::-webkit-scrollbar-track {
  background: #2c3e50;
  border-radius: 3px;
}

.history-display::-webkit-scrollbar-thumb {
  background: #7f8c8d;
  border-radius: 3px;
}

.history-display::-webkit-scrollbar-thumb:hover {
  background: #95a5a6;
}

.display {
  margin-bottom: 20px;
}

.display input {
  width: 100%;
  height: 80px;
  background: #34495e;
  border: none;
  border-radius: 10px;
  color: white;
  font-size: 2rem;
  text-align: right;
  padding: 0 20px;
  outline: none;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

.buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 15px;
}

.btn {
  height: 60px;
  border: none;
  border-radius: 10px;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn:active {
  transform: translateY(0);
}

.number {
  background: #3498db;
  color: white;
}

.number:hover {
  background: #2980b9;
}

.operator {
  background: #e67e22;
  color: white;
}

.operator:hover {
  background: #d35400;
}

.equals {
  background: #27ae60;
  color: white;
}

.equals:hover {
  background: #229954;
}

.clear {
  background: #e74c3c;
  color: white;
}

.clear:hover {
  background: #c0392b;
}

.zero {
  grid-column: span 2;
}

/* Responsive design */
@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  .calculator {
    padding: 15px;
  }

  .display input {
    height: 70px;
    font-size: 1.8rem;
  }

  .btn {
    height: 50px;
    font-size: 1rem;
  }

  .buttons {
    gap: 10px;
  }
}
