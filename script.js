// Mendapatkan elemen display
const display = document.getElementById('result');

// Variabel untuk menyimpan operasi
let currentInput = '';
let operator = '';
let previousInput = '';

// Fungsi untuk menambahkan input ke display
function appendToDisplay(value) {
    const lastChar = display.value.slice(-1);
    
    // Mencegah operator berturut-turut
    if (['+', '-', '*', '/'].includes(value) && ['+', '-', '*', '/'].includes(lastChar)) {
        return;
    }
    
    // Mencegah titik desimal berturut-turut
    if (value === '.' && lastChar === '.') {
        return;
    }
    
    // Mencegah multiple titik dalam satu angka
    if (value === '.') {
        const parts = display.value.split(/[+\-*/]/);
        const currentNumber = parts[parts.length - 1];
        if (currentNumber.includes('.')) {
            return;
        }
    }
    
    display.value += value;
}

// Fungsi untuk menghapus semua input
function clearDisplay() {
    display.value = '';
    currentInput = '';
    operator = '';
    previousInput = '';
}

// Fungsi untuk menghapus karakter terakhir
function deleteLast() {
    display.value = display.value.slice(0, -1);
}

// Fungsi untuk menghitung hasil
function calculate() {
    try {
        if (display.value === '') {
            return;
        }
        
        // Mengganti × dengan * untuk evaluasi
        let expression = display.value.replace(/×/g, '*');
        
        // Validasi ekspresi
        if (/[+\-*/]$/.test(expression)) {
            // Jika berakhir dengan operator, hapus operator tersebut
            expression = expression.slice(0, -1);
        }
        
        // Evaluasi ekspresi matematika
        const result = eval(expression);
        
        // Cek jika hasil adalah infinity atau NaN
        if (!isFinite(result)) {
            display.value = 'Error';
            return;
        }
        
        // Membulatkan hasil jika perlu
        display.value = parseFloat(result.toFixed(10)).toString();
        
    } catch (error) {
        display.value = 'Error';
    }
}

// Event listener untuk keyboard input
document.addEventListener('keydown', function(event) {
    const key = event.key;
    
    // Angka dan titik desimal
    if (/[0-9.]/.test(key)) {
        appendToDisplay(key);
    }
    
    // Operator
    else if (['+', '-', '*', '/'].includes(key)) {
        if (key === '*') {
            appendToDisplay('*');
        } else {
            appendToDisplay(key);
        }
    }
    
    // Enter atau = untuk menghitung
    else if (key === 'Enter' || key === '=') {
        event.preventDefault();
        calculate();
    }
    
    // Escape atau c untuk clear
    else if (key === 'Escape' || key.toLowerCase() === 'c') {
        clearDisplay();
    }
    
    // Backspace untuk menghapus karakter terakhir
    else if (key === 'Backspace') {
        deleteLast();
    }
});

// Mencegah input manual pada display
display.addEventListener('keypress', function(event) {
    event.preventDefault();
});

// Fokus pada display saat halaman dimuat
window.addEventListener('load', function() {
    display.focus();
});
