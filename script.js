// Mendapatkan elemen display
const display = document.getElementById("result");
const historyDisplay = document.getElementById("history");

// Variabel untuk menyimpan operasi
let currentInput = "";
let operator = "";
let previousInput = "";

// Array untuk menyimpan history
let calculationHistory = [];

// Fungsi untuk menambahkan input ke display
function appendToDisplay(value) {
  const lastChar = display.value.slice(-1);

  // Mencegah operator berturut-turut
  if (
    ["+", "-", "*", "/"].includes(value) &&
    ["+", "-", "*", "/"].includes(lastChar)
  ) {
    return;
  }

  // Mencegah titik desimal berturut-turut
  if (value === "." && lastChar === ".") {
    return;
  }

  // Mencegah multiple titik dalam satu angka
  if (value === ".") {
    const parts = display.value.split(/[+\-*/]/);
    const currentNumber = parts[parts.length - 1];
    if (currentNumber.includes(".")) {
      return;
    }
  }

  display.value += value;
}

// Fungsi untuk menghapus semua input
function clearDisplay() {
  display.value = "";
  currentInput = "";
  operator = "";
  previousInput = "";
}

// Fungsi untuk menghapus karakter terakhir
function deleteLast() {
  display.value = display.value.slice(0, -1);
}

// Fungsi untuk menambahkan item ke history
function addToHistory(expression, result) {
  const historyItem = {
    expression: expression,
    result: result,
    timestamp: new Date(),
  };

  calculationHistory.push(historyItem);

  // Batasi history maksimal 50 item
  if (calculationHistory.length > 50) {
    calculationHistory.shift();
  }

  updateHistoryDisplay();
}

// Fungsi untuk memperbarui tampilan history
function updateHistoryDisplay() {
  historyDisplay.innerHTML = "";

  if (calculationHistory.length === 0) {
    historyDisplay.innerHTML =
      '<div style="color: #7f8c8d; text-align: center; font-style: italic;">Belum ada perhitungan</div>';
    return;
  }

  calculationHistory.forEach((item, index) => {
    const historyItem = document.createElement("div");
    historyItem.className = "history-item";
    historyItem.innerHTML = `
      <div class="history-expression">${item.expression}</div>
      <div class="history-result">= ${item.result}</div>
    `;

    // Tambahkan event listener untuk mengklik history item
    historyItem.addEventListener("click", () => {
      display.value = item.result;
    });

    historyItem.style.cursor = "pointer";
    historyItem.title = "Klik untuk menggunakan hasil ini";

    historyDisplay.appendChild(historyItem);
  });

  // Scroll ke bawah untuk menampilkan item terbaru
  historyDisplay.scrollTop = historyDisplay.scrollHeight;
}

// Fungsi untuk menghapus history
function clearHistory() {
  calculationHistory = [];
  updateHistoryDisplay();
}

// Fungsi untuk menghitung hasil
function calculate() {
  try {
    if (display.value === "") {
      return;
    }

    const originalExpression = display.value;

    // Mengganti × dengan * untuk evaluasi
    let expression = display.value.replace(/×/g, "*");

    // Validasi ekspresi
    if (/[+\-*/]$/.test(expression)) {
      // Jika berakhir dengan operator, hapus operator tersebut
      expression = expression.slice(0, -1);
    }

    // Evaluasi ekspresi matematika
    const result = eval(expression);

    // Cek jika hasil adalah infinity atau NaN
    if (!isFinite(result)) {
      display.value = "Error";
      return;
    }

    // Membulatkan hasil jika perlu
    const finalResult = parseFloat(result.toFixed(10)).toString();

    // Tambahkan ke history sebelum mengubah display
    addToHistory(originalExpression, finalResult);

    // Update display dengan hasil
    display.value = finalResult;
  } catch (error) {
    display.value = "Error";
  }
}

// Event listener untuk keyboard input
document.addEventListener("keydown", function (event) {
  const key = event.key;

  // Angka dan titik desimal
  if (/[0-9.]/.test(key)) {
    appendToDisplay(key);
  }

  // Operator
  else if (["+", "-", "*", "/"].includes(key)) {
    if (key === "*") {
      appendToDisplay("*");
    } else {
      appendToDisplay(key);
    }
  }

  // Enter atau = untuk menghitung
  else if (key === "Enter" || key === "=") {
    event.preventDefault();
    calculate();
  }

  // Escape atau c untuk clear
  else if (key === "Escape" || key.toLowerCase() === "c") {
    clearDisplay();
  }

  // Backspace untuk menghapus karakter terakhir
  else if (key === "Backspace") {
    deleteLast();
  }
});

// Mencegah input manual pada display
display.addEventListener("keypress", function (event) {
  event.preventDefault();
});

// Fokus pada display saat halaman dimuat
window.addEventListener("load", function () {
  display.focus();
  updateHistoryDisplay(); // Inisialisasi tampilan history
});
